
import React, { createContext, useContext, useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  FileText,
  BarChartHorizontal,
  MessageSquare,
  Settings,
  ChevronLeft,
  ChevronRight,
  Receipt,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

// Create sidebar context
type SidebarContextType = {
  isOpen: boolean;
  toggle: () => void;
  close: () => void;
};

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Sidebar provider component
export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(true);
  const [theme, setTheme] = useState<string>('light');
  const isMobile = useIsMobile();

  // Initialize theme from localStorage and close sidebar on mobile
  useEffect(() => {
    const storedTheme = localStorage.getItem('theme') || 'light';
    setTheme(storedTheme);
    document.documentElement.classList.toggle('dark', storedTheme === 'dark');
    
    // Auto-close sidebar on mobile
    if (isMobile) {
      setIsOpen(false);
    } else {
      setIsOpen(true);
    }
  }, [isMobile]);

  const toggle = () => setIsOpen(!isOpen);
  const close = () => setIsOpen(false);

  return (
    <SidebarContext.Provider value={{ isOpen, toggle, close }}>
      <div className="flex min-h-screen w-full">
        {children}
      </div>
    </SidebarContext.Provider>
  );
}

// Custom hook to use sidebar context
export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}

// Sidebar trigger component
export function SidebarTrigger({ children, className }: { children?: React.ReactNode, className?: string }) {
  const { toggle } = useSidebar();
  return (
    <Button 
      variant="ghost" 
      size="icon" 
      onClick={toggle} 
      className={className}
    >
      {children}
    </Button>
  );
}

// Nav item type
type NavItemProps = {
  href: string;
  icon: React.ElementType;
  label: string;
  end?: boolean;
};

// Nav item component
const NavItem = ({ href, icon: Icon, label, end }: NavItemProps) => {
  const { isOpen, close } = useSidebar();
  const isMobile = useIsMobile();

  const handleClick = () => {
    // Close sidebar on mobile when navigation item is clicked
    if (isMobile) {
      close();
    }
  };

  return (
    <NavLink
      to={href}
      end={end}
      onClick={handleClick}
      className={({ isActive }) => cn(
        "flex items-center gap-x-3 rounded-md px-3 py-2 text-sm font-medium transition-all",
        isActive
          ? "bg-sidebar-accent text-sidebar-accent-foreground"
          : "text-sidebar-foreground hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground",
        !isOpen && "justify-center px-2"
      )}
    >
      <Icon className={cn("h-5 w-5", !isOpen && "h-6 w-6")} />
      {isOpen && <span>{label}</span>}
    </NavLink>
  );
};

// Main sidebar component
export function Sidebar({ className }: { className?: string }) {
  const { isOpen, toggle, close } = useSidebar();
  const { isAdmin } = useAuth();
  const isMobile = useIsMobile();
  
  // Define navigation items based on user role
  const adminNavItems = [
    { href: "/", icon: LayoutDashboard, label: "Dashboard", end: true },
    { href: "/inventory", icon: Package, label: "Inventory" },
    { href: "/sales", icon: ShoppingCart, label: "Sales" },
    { href: "/staff", icon: Users, label: "Staff" },
    { href: "/expenses", icon: Receipt, label: "Expenses" },
    { href: "/analytics", icon: BarChartHorizontal, label: "Analytics" },
    { href: "/messaging", icon: MessageSquare, label: "Messaging" },
    { href: "/settings", icon: Settings, label: "Settings" },
  ];
  
  const shopkeeperNavItems = [
    { href: "/", icon: LayoutDashboard, label: "Dashboard", end: true },
    { href: "/inventory", icon: Package, label: "Inventory" },
    { href: "/sales", icon: ShoppingCart, label: "Sales" },
    { href: "/messaging", icon: MessageSquare, label: "Messaging" },
    { href: "/settings", icon: Settings, label: "Settings" },
  ];
  
  const navItems = isAdmin() ? adminNavItems : shopkeeperNavItems;

  // Create an overlay that closes the sidebar when clicked (mobile only)
  const renderOverlay = () => {
    if (isMobile && isOpen) {
      return (
        <div 
          className="fixed inset-0 bg-black/50 z-20" 
          onClick={close}
          aria-hidden="true"
        />
      );
    }
    return null;
  };

  return (
    <>
      {renderOverlay()}
      <aside
        className={cn(
          "fixed md:relative flex flex-col bg-sidebar border-r transition-all duration-300 ease-in-out z-30 h-screen flex-shrink-0",
          isOpen ? "w-64" : "w-[70px]",
          isMobile && !isOpen && "-translate-x-full md:translate-x-0",
          className
        )}
      >
        <div className="flex h-16 items-center justify-between px-4 py-4">
       
          {/* Desktop toggle button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggle}
            className="absolute -right-3 top-5 h-6 w-6 rounded-full border bg-background shadow-sm hidden md:flex"
          >
            
          </Button>
          {/* Mobile close button */}
          {isMobile && isOpen && (
            <Button
              variant="ghost"
              size="icon"
              onClick={close}
              className="h-8 w-8 md:hidden"
            >
            
            </Button>
          )}
        </div>
        
        <Separator />
        
        <ScrollArea className="flex-1 py-4">
          <nav className="grid gap-2 px-2">
            {navItems.map((item) => (
              <NavItem 
                key={item.href} 
                href={item.href} 
                icon={item.icon} 
                label={item.label} 
                end={item.end}
              />
            ))}
          </nav>
        </ScrollArea>
        
        <Separator />
        
        <div className="p-4">
          <p className={cn(
            "text-sm text-sidebar-foreground/70 transition-all",
            !isOpen && "text-center text-xs"
          )}>
            {isOpen ? "© 2025 Z&D stationery" : "© 2025"}
          </p>
        </div>
      </aside>
    </>
  );
}
